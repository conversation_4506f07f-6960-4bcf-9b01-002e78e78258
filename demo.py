#!/usr/bin/env python3
"""
3D超级玛丽游戏演示脚本
展示游戏的主要功能和特性
"""

import subprocess
import sys
import time

def print_banner():
    """打印游戏横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🍄 3D超级玛丽游戏 🍄                      ║
    ║                                                              ║
    ║              使用Python和Pygame开发的3D版超级玛丽             ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_features():
    """打印游戏特性"""
    features = """
    🎮 游戏特性:
    
    ✨ 3D视觉效果
    • 完全3D化的游戏世界
    • 立体的角色和场景元素
    • 动态3D摄像机系统
    
    🏃 角色控制
    • WASD键控制玛丽在3D空间中移动
    • 空格键跳跃
    • 真实的物理引擎和重力系统
    
    👾 敌人系统
    • 栗子怪(Goomba) - 可以踩踏消灭
    • 乌龟(Koopa) - 踩踏后变成龟壳，可以踢动
    • 智能AI巡逻和碰撞检测
    
    💰 道具收集
    • 金币收集系统
    • 分数统计
    • 生命值系统
    
    🏗️ 3D场景
    • 立体的地面和平台
    • 3D管道和障碍物
    • 问号方块和砖块
    
    🎯 游戏机制
    • 碰撞检测系统
    • 无敌状态和受伤机制
    • 游戏重置功能
    """
    print(features)

def print_controls():
    """打印控制说明"""
    controls = """
    🎮 控制说明:
    
    移动控制:
    • W/↑ - 向前移动
    • S/↓ - 向后移动  
    • A/← - 向左移动
    • D/→ - 向右移动
    • 空格 - 跳跃
    
    游戏控制:
    • R - 重置游戏
    • ESC - 退出游戏
    
    游戏提示:
    • 踩踏敌人可以消灭它们并获得分数
    • 收集金币增加分数
    • 避免被敌人从侧面碰到
    • 利用3D空间的优势绕过障碍
    """
    print(controls)

def run_simple_version():
    """运行简化版游戏"""
    print("\n🚀 启动简化版3D超级玛丽...")
    print("这个版本展示了基础的3D渲染和控制系统")
    time.sleep(2)
    
    try:
        subprocess.run(["uv", "run", "python", "mario_3d_simple.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 简化版游戏启动失败")
    except KeyboardInterrupt:
        print("\n⏹️ 简化版游戏已停止")

def run_full_version():
    """运行完整版游戏"""
    print("\n🚀 启动完整版3D超级玛丽...")
    print("这个版本包含完整的游戏机制：敌人、道具、碰撞检测等")
    time.sleep(2)
    
    try:
        subprocess.run(["uv", "run", "python", "mario_3d.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 完整版游戏启动失败")
    except KeyboardInterrupt:
        print("\n⏹️ 完整版游戏已停止")

def show_menu():
    """显示主菜单"""
    menu = """
    📋 选择要运行的版本:
    
    1. 简化版 - 基础3D渲染和控制
    2. 完整版 - 包含敌人、道具等完整功能
    3. 查看项目结构
    4. 退出
    
    请输入选项 (1-4): """
    
    return input(menu).strip()

def show_project_structure():
    """显示项目结构"""
    structure = """
    📁 项目结构:
    
    test_kilocode/
    ├── mario_3d.py              # 主游戏文件（完整版）
    ├── mario_3d_simple.py       # 简化版游戏
    ├── player.py                # 玩家角色系统（Ursina版本）
    ├── enemy.py                 # 敌人AI系统
    ├── game_objects.py          # 游戏对象（Ursina版本）
    ├── demo.py                  # 演示脚本
    ├── README.md                # 项目文档
    ├── pyproject.toml           # 项目配置
    └── uv.lock                  # 依赖锁定文件
    
    🔧 技术栈:
    • Python 3.13+
    • Pygame 2.6.1 - 2D/3D图形和游戏开发
    • UV - 现代Python包管理器
    
    🎯 架构设计:
    • 模块化设计，各系统独立
    • 3D向量数学库
    • 面向对象的游戏实体
    • 事件驱动的游戏循环
    • 可扩展的敌人和道具系统
    """
    print(structure)

def main():
    """主函数"""
    print_banner()
    
    while True:
        choice = show_menu()
        
        if choice == '1':
            print_controls()
            run_simple_version()
        elif choice == '2':
            print_features()
            print_controls()
            run_full_version()
        elif choice == '3':
            show_project_structure()
        elif choice == '4':
            print("\n👋 感谢体验3D超级玛丽游戏！")
            break
        else:
            print("\n❌ 无效选项，请重新选择")
        
        print("\n" + "="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 游戏演示已退出，感谢体验！")
        sys.exit(0)
