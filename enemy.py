"""
3D超级玛丽 - 敌人系统
包含各种敌人的AI和行为逻辑
"""

import pygame
import math
import random
from typing import List, Tuple
from dataclasses import dataclass

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
BROWN = (139, 69, 19)
DARK_BROWN = (101, 67, 33)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)

@dataclass
class Vector3D:
    """3D向量类"""
    x: float
    y: float
    z: float
    
    def __add__(self, other):
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other):
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar):
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def distance_to(self, other):
        """计算到另一个向量的距离"""
        dx = self.x - other.x
        dy = self.y - other.y
        dz = self.z - other.z
        return math.sqrt(dx*dx + dy*dy + dz*dz)

class Goomba3D:
    """3D栗子怪敌人类"""
    
    def __init__(self, position: Vector3D):
        self.position = position
        self.velocity = Vector3D(-0.1, 0, 0)  # 向左移动
        self.gravity = -0.03
        self.health = 1
        self.is_alive = True
        self.on_ground = False
        
        # AI属性
        self.start_x = position.x
        self.patrol_range = 5
        self.direction = -1  # -1=左, 1=右
        self.animation_timer = 0
        
        # 状态
        self.squashed = False
        self.squash_timer = 0
        
    def update(self, mario, blocks):
        """更新敌人AI"""
        if not self.is_alive:
            return
            
        self.animation_timer += 1/60  # 假设60FPS
        
        # 应用重力
        self.velocity.y += self.gravity
        
        # 巡逻逻辑
        if abs(self.position.x - self.start_x) > self.patrol_range:
            self.direction *= -1  # 转向
            self.velocity.x = self.direction * 0.1
            
        # 更新位置
        new_position = self.position + self.velocity
        
        # 简单的碰撞检测
        self.check_collisions(new_position, blocks)
        
        # 地面检测
        if self.position.y <= 0.5:
            self.position.y = 0.5
            self.velocity.y = 0
            self.on_ground = True
        else:
            self.on_ground = False
            
        # 检查与玛丽的碰撞
        self.check_mario_collision(mario)
        
        # 处理被压扁状态
        if self.squashed:
            self.squash_timer -= 1/60
            if self.squash_timer <= 0:
                self.is_alive = False
                
    def check_collisions(self, new_position, blocks):
        """检查与方块的碰撞"""
        for block in blocks:
            if (abs(new_position.x - block.position.x) < 1.0 and
                abs(new_position.y - block.position.y) < 1.0 and
                abs(new_position.z - block.position.z) < 1.0):
                
                # 水平碰撞，转向
                if abs(self.position.x - block.position.x) < 1.0:
                    self.direction *= -1
                    self.velocity.x = self.direction * 0.1
                    new_position.x = self.position.x
                    
                # 垂直碰撞
                if self.velocity.y < 0 and self.position.y > block.position.y:
                    new_position.y = block.position.y + 1.0
                    self.velocity.y = 0
                    self.on_ground = True
                    
        self.position = new_position
        
    def check_mario_collision(self, mario):
        """检查与玛丽的碰撞"""
        if not self.is_alive or self.squashed:
            return
            
        distance = self.position.distance_to(mario.position)
        if distance < 1.2:
            # 检查玛丽是否从上方踩踏
            if mario.position.y > self.position.y + 0.3 and mario.velocity.y < 0:
                # 被踩踏
                self.take_damage(mario)
                mario.velocity.y = 0.3  # 给玛丽一个小跳跃
                mario.score += 100
            else:
                # 玛丽受伤
                if not mario.invincible:
                    mario.take_damage()
                    
    def take_damage(self, mario):
        """受到伤害"""
        self.health -= 1
        if self.health <= 0:
            self.squashed = True
            self.squash_timer = 0.5  # 0.5秒后消失
            
    def draw(self, screen, camera):
        """绘制3D栗子怪"""
        if not self.is_alive:
            return
            
        # 根据状态确定颜色和大小
        if self.squashed:
            color = DARK_BROWN
            height = 0.2
        else:
            color = BROWN
            height = 0.8
            
        # 简单的移动动画
        bob = math.sin(self.animation_timer * 8) * 0.1 if not self.squashed else 0
        
        # 栗子怪的身体
        body_points = [
            Vector3D(self.position.x - 0.4, self.position.y + bob, self.position.z - 0.4),
            Vector3D(self.position.x + 0.4, self.position.y + bob, self.position.z - 0.4),
            Vector3D(self.position.x + 0.4, self.position.y + height + bob, self.position.z - 0.4),
            Vector3D(self.position.x - 0.4, self.position.y + height + bob, self.position.z - 0.4),
            Vector3D(self.position.x - 0.4, self.position.y + bob, self.position.z + 0.4),
            Vector3D(self.position.x + 0.4, self.position.y + bob, self.position.z + 0.4),
            Vector3D(self.position.x + 0.4, self.position.y + height + bob, self.position.z + 0.4),
            Vector3D(self.position.x - 0.4, self.position.y + height + bob, self.position.z + 0.4),
        ]
        
        # 投影到2D
        projected_points = [camera.project_point(p) for p in body_points]
        
        # 绘制立方体的面
        faces = [
            [0, 1, 2, 3],  # 前面
            [4, 5, 6, 7],  # 后面
            [0, 1, 5, 4],  # 底面
            [2, 3, 7, 6],  # 顶面
            [0, 3, 7, 4],  # 左面
            [1, 2, 6, 5],  # 右面
        ]
        
        for face in faces:
            points_2d = [projected_points[i] for i in face]
            if len(points_2d) >= 3:
                pygame.draw.polygon(screen, color, points_2d)
                pygame.draw.polygon(screen, BLACK, points_2d, 2)
                
        # 绘制眼睛（如果没有被压扁）
        if not self.squashed:
            eye_left = camera.project_point(Vector3D(self.position.x - 0.15, self.position.y + height * 0.7 + bob, self.position.z - 0.41))
            eye_right = camera.project_point(Vector3D(self.position.x + 0.15, self.position.y + height * 0.7 + bob, self.position.z - 0.41))
            pygame.draw.circle(screen, WHITE, eye_left, 8)
            pygame.draw.circle(screen, WHITE, eye_right, 8)
            pygame.draw.circle(screen, BLACK, eye_left, 4)
            pygame.draw.circle(screen, BLACK, eye_right, 4)

class Koopa3D:
    """3D乌龟敌人类"""
    
    def __init__(self, position: Vector3D):
        self.position = position
        self.velocity = Vector3D(0.08, 0, 0)  # 向右移动
        self.gravity = -0.03
        self.health = 2
        self.is_alive = True
        self.on_ground = False
        
        # 状态
        self.in_shell = False
        self.shell_timer = 0
        self.shell_speed = 0.3
        
        # AI属性
        self.start_x = position.x
        self.patrol_range = 6
        self.direction = 1  # -1=左, 1=右
        self.animation_timer = 0
        
    def update(self, mario, blocks):
        """更新乌龟AI"""
        if not self.is_alive:
            return
            
        self.animation_timer += 1/60
        
        # 应用重力
        self.velocity.y += self.gravity
        
        if self.in_shell:
            # 龟壳状态
            self.shell_timer -= 1/60
            if self.shell_timer <= 0:
                self.in_shell = False
                self.health = 2
                
            # 如果龟壳在移动
            if abs(self.velocity.x) > 0.01:
                # 检查碰撞并反弹
                for block in blocks:
                    if (abs(self.position.x - block.position.x) < 1.0 and
                        abs(self.position.y - block.position.y) < 1.0 and
                        abs(self.position.z - block.position.z) < 1.0):
                        self.velocity.x *= -1
                        break
        else:
            # 正常巡逻
            if abs(self.position.x - self.start_x) > self.patrol_range:
                self.direction *= -1
                self.velocity.x = self.direction * 0.08
                
        # 更新位置
        new_position = self.position + self.velocity
        
        # 碰撞检测
        self.check_collisions(new_position, blocks)
        
        # 地面检测
        if self.position.y <= 0.5:
            self.position.y = 0.5
            self.velocity.y = 0
            self.on_ground = True
        else:
            self.on_ground = False
            
        # 检查与玛丽的碰撞
        self.check_mario_collision(mario)
        
    def check_collisions(self, new_position, blocks):
        """检查碰撞"""
        for block in blocks:
            if (abs(new_position.x - block.position.x) < 1.0 and
                abs(new_position.y - block.position.y) < 1.0 and
                abs(new_position.z - block.position.z) < 1.0):
                
                if abs(self.position.x - block.position.x) < 1.0:
                    if not self.in_shell:
                        self.direction *= -1
                        self.velocity.x = self.direction * 0.08
                    else:
                        self.velocity.x *= -1
                    new_position.x = self.position.x
                    
                if self.velocity.y < 0 and self.position.y > block.position.y:
                    new_position.y = block.position.y + 1.0
                    self.velocity.y = 0
                    self.on_ground = True
                    
        self.position = new_position
        
    def check_mario_collision(self, mario):
        """检查与玛丽的碰撞"""
        if not self.is_alive:
            return
            
        distance = self.position.distance_to(mario.position)
        if distance < 1.2:
            if mario.position.y > self.position.y + 0.3 and mario.velocity.y < 0:
                # 被踩踏
                self.take_damage(mario)
                mario.velocity.y = 0.3
                mario.score += 200
            elif self.in_shell and abs(self.velocity.x) < 0.01:
                # 踢龟壳
                direction = 1 if mario.position.x < self.position.x else -1
                self.velocity.x = direction * self.shell_speed
                mario.score += 50
            elif not mario.invincible:
                # 玛丽受伤
                mario.take_damage()
                
    def take_damage(self, mario):
        """受到伤害"""
        self.health -= 1
        if self.health <= 0:
            if not self.in_shell:
                # 进入龟壳状态
                self.in_shell = True
                self.shell_timer = 5.0  # 5秒后恢复
                self.velocity.x = 0
            else:
                # 彻底死亡
                self.is_alive = False
                
    def draw(self, screen, camera):
        """绘制3D乌龟"""
        if not self.is_alive:
            return
            
        if self.in_shell:
            # 绘制龟壳
            color = GREEN
            height = 0.6
        else:
            # 绘制完整乌龟
            color = GREEN
            height = 1.0
            
        # 乌龟的身体
        body_points = [
            Vector3D(self.position.x - 0.4, self.position.y, self.position.z - 0.4),
            Vector3D(self.position.x + 0.4, self.position.y, self.position.z - 0.4),
            Vector3D(self.position.x + 0.4, self.position.y + height, self.position.z - 0.4),
            Vector3D(self.position.x - 0.4, self.position.y + height, self.position.z - 0.4),
            Vector3D(self.position.x - 0.4, self.position.y, self.position.z + 0.4),
            Vector3D(self.position.x + 0.4, self.position.y, self.position.z + 0.4),
            Vector3D(self.position.x + 0.4, self.position.y + height, self.position.z + 0.4),
            Vector3D(self.position.x - 0.4, self.position.y + height, self.position.z + 0.4),
        ]
        
        # 投影到2D
        projected_points = [camera.project_point(p) for p in body_points]
        
        # 绘制立方体的面
        faces = [
            [0, 1, 2, 3],  # 前面
            [4, 5, 6, 7],  # 后面
            [0, 1, 5, 4],  # 底面
            [2, 3, 7, 6],  # 顶面
            [0, 3, 7, 4],  # 左面
            [1, 2, 6, 5],  # 右面
        ]
        
        for face in faces:
            points_2d = [projected_points[i] for i in face]
            if len(points_2d) >= 3:
                pygame.draw.polygon(screen, color, points_2d)
                pygame.draw.polygon(screen, BLACK, points_2d, 2)
                
        # 如果不在龟壳状态，绘制头部
        if not self.in_shell:
            head_pos = Vector3D(self.position.x + self.direction * 0.3, self.position.y + 0.3, self.position.z)
            head_center = camera.project_point(head_pos)
            pygame.draw.circle(screen, color, head_center, 12)
            pygame.draw.circle(screen, BLACK, head_center, 12, 2)

class EnemyManager:
    """敌人管理器"""
    
    def __init__(self):
        self.enemies = []
        
    def add_goomba(self, position: Vector3D):
        """添加栗子怪"""
        self.enemies.append(Goomba3D(position))
        
    def add_koopa(self, position: Vector3D):
        """添加乌龟"""
        self.enemies.append(Koopa3D(position))
        
    def update_all(self, mario, blocks):
        """更新所有敌人"""
        # 移除死亡的敌人
        self.enemies = [enemy for enemy in self.enemies if enemy.is_alive]
        
        # 更新存活的敌人
        for enemy in self.enemies:
            enemy.update(mario, blocks)
            
    def draw_all(self, screen, camera):
        """绘制所有敌人"""
        for enemy in self.enemies:
            enemy.draw(screen, camera)
            
    def get_enemy_count(self):
        """获取敌人数量"""
        return len(self.enemies)
