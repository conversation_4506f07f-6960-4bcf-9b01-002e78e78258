"""
3D超级玛丽 - 玩家角色控制系统
"""

from ursina import *
import math

class <PERSON>(Entity):
    """3D玛丽角色类"""
    
    def __init__(self):
        super().__init__()
        
        # 基础属性
        self.model = 'cube'
        self.color = color.red
        self.scale = (0.8, 1.6, 0.8)  # 玛丽的身材比例
        self.position = (0, 2, 0)
        
        # 物理属性
        self.velocity = Vec3(0, 0, 0)
        self.speed = 5
        self.jump_power = 15
        self.gravity = -30
        self.max_fall_speed = -20
        self.on_ground = False
        self.ground_check_distance = 0.1
        
        # 状态属性
        self.health = 3
        self.score = 0
        self.power_level = 1  # 1=小玛丽, 2=大玛丽, 3=火焰玛丽
        self.invincible = False
        self.invincible_timer = 0
        
        # 动画属性
        self.animation_timer = 0
        self.is_moving = False
        self.facing_direction = 1  # 1=右, -1=左
        
        # 音效（占位符）
        self.jump_sound = None
        self.power_up_sound = None
        self.damage_sound = None
        
    def input(self, key):
        """处理输入"""
        if key == 'space' and self.on_ground:
            self.jump()
        elif key == 'j' and self.on_ground:  # 备用跳跃键
            self.jump()
            
    def update(self):
        """每帧更新"""
        self.handle_movement()
        self.apply_physics()
        self.check_ground_collision()
        self.update_animation()
        self.update_invincibility()
        
    def handle_movement(self):
        """处理移动输入"""
        # 重置水平速度
        horizontal_velocity = Vec3(0, 0, 0)
        self.is_moving = False
        
        # 左右移动
        if held_keys['a'] or held_keys['left arrow']:
            horizontal_velocity.x = -self.speed
            self.facing_direction = -1
            self.is_moving = True
            
        if held_keys['d'] or held_keys['right arrow']:
            horizontal_velocity.x = self.speed
            self.facing_direction = 1
            self.is_moving = True
            
        # 前后移动（3D特有）
        if held_keys['w'] or held_keys['up arrow']:
            horizontal_velocity.z = -self.speed
            self.is_moving = True
            
        if held_keys['s'] or held_keys['down arrow']:
            horizontal_velocity.z = self.speed
            self.is_moving = True
            
        # 应用水平移动
        self.velocity.x = horizontal_velocity.x
        self.velocity.z = horizontal_velocity.z
        
    def apply_physics(self):
        """应用物理效果"""
        # 应用重力
        if not self.on_ground:
            self.velocity.y += self.gravity * time.dt
            
        # 限制最大下落速度
        if self.velocity.y < self.max_fall_speed:
            self.velocity.y = self.max_fall_speed
            
        # 更新位置
        self.position += self.velocity * time.dt
        
    def check_ground_collision(self):
        """检查地面碰撞"""
        # 简单的地面检测（y=0为地面）
        if self.position.y <= 1:  # 玛丽高度的一半
            self.position = (self.position.x, 1, self.position.z)
            self.velocity.y = 0
            self.on_ground = True
        else:
            self.on_ground = False
            
    def jump(self):
        """跳跃"""
        if self.on_ground:
            self.velocity.y = self.jump_power
            self.on_ground = False
            # 播放跳跃音效
            if self.jump_sound:
                self.jump_sound.play()
                
    def update_animation(self):
        """更新动画"""
        self.animation_timer += time.dt
        
        # 简单的移动动画 - 轻微摇摆
        if self.is_moving:
            sway = math.sin(self.animation_timer * 10) * 0.1
            self.rotation_z = sway * 5  # 轻微倾斜
        else:
            self.rotation_z = 0
            
        # 面向方向
        if self.facing_direction == -1:
            self.scale_x = -abs(self.scale_x)
        else:
            self.scale_x = abs(self.scale_x)
            
    def update_invincibility(self):
        """更新无敌状态"""
        if self.invincible:
            self.invincible_timer -= time.dt
            
            # 闪烁效果
            if int(self.invincible_timer * 10) % 2:
                self.color = color.white
            else:
                self.color = self.get_mario_color()
                
            if self.invincible_timer <= 0:
                self.invincible = False
                self.color = self.get_mario_color()
                
    def get_mario_color(self):
        """根据能力等级获取玛丽颜色"""
        if self.power_level == 1:
            return color.red  # 小玛丽
        elif self.power_level == 2:
            return color.orange  # 大玛丽
        elif self.power_level == 3:
            return color.yellow  # 火焰玛丽
        else:
            return color.red
            
    def power_up(self):
        """升级能力"""
        if self.power_level < 3:
            self.power_level += 1
            
            # 改变大小和颜色
            if self.power_level == 2:
                self.scale = (0.8, 2.0, 0.8)  # 变大
            elif self.power_level == 3:
                self.scale = (0.8, 2.0, 0.8)  # 保持大小，获得火焰能力
                
            self.color = self.get_mario_color()
            
            # 播放升级音效
            if self.power_up_sound:
                self.power_up_sound.play()
                
    def take_damage(self):
        """受到伤害"""
        if self.invincible:
            return
            
        if self.power_level > 1:
            # 降级
            self.power_level -= 1
            if self.power_level == 1:
                self.scale = (0.8, 1.6, 0.8)  # 变小
            self.color = self.get_mario_color()
            
            # 短暂无敌
            self.invincible = True
            self.invincible_timer = 2.0
            
        else:
            # 失去生命
            self.health -= 1
            if self.health <= 0:
                self.game_over()
            else:
                # 重置位置
                self.position = (0, 2, 0)
                self.velocity = Vec3(0, 0, 0)
                
        # 播放受伤音效
        if self.damage_sound:
            self.damage_sound.play()
            
    def game_over(self):
        """游戏结束"""
        print(f"游戏结束! 最终得分: {self.score}")
        # 这里可以添加游戏结束逻辑
        
    def add_score(self, points):
        """增加分数"""
        self.score += points
        
    def collect_coin(self):
        """收集金币"""
        self.add_score(100)
        
    def collect_powerup(self, powerup_type):
        """收集道具"""
        if powerup_type == "mushroom":
            self.power_up()
            self.add_score(1000)
        elif powerup_type == "fire_flower":
            self.power_level = 3
            self.scale = (0.8, 2.0, 0.8)
            self.color = self.get_mario_color()
            self.add_score(1000)
        elif powerup_type == "star":
            self.invincible = True
            self.invincible_timer = 10.0
            self.add_score(1000)
            
    def fire_projectile(self):
        """发射火球（火焰玛丽专用）"""
        if self.power_level >= 3:
            # 创建火球实体
            fireball = Entity(
                model='sphere',
                color=color.orange,
                scale=0.3,
                position=self.position + Vec3(self.facing_direction * 1, 0, 0)
            )
            
            # 给火球添加移动逻辑
            fireball.velocity = Vec3(self.facing_direction * 10, 5, 0)
            
            def update_fireball():
                fireball.position += fireball.velocity * time.dt
                fireball.velocity.y -= 20 * time.dt  # 重力
                
                # 如果火球落地或飞出屏幕就销毁
                if fireball.position.y < 0 or abs(fireball.position.x) > 50:
                    destroy(fireball)
                    
            fireball.update = update_fireball
            
    def reset_position(self):
        """重置位置"""
        self.position = (0, 2, 0)
        self.velocity = Vec3(0, 0, 0)
        
    def get_status_info(self):
        """获取状态信息"""
        return {
            'health': self.health,
            'score': self.score,
            'power_level': self.power_level,
            'position': self.position
        }
