#!/usr/bin/env python3
"""
3D超级玛丽游戏 - 简化版本
使用Pygame和基础3D投影实现
"""

import pygame
import math
import sys
from typing import List, Tuple, Dict
from dataclasses import dataclass

# 初始化Pygame
pygame.init()

# 游戏常量
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
BROWN = (139, 69, 19)
GRAY = (128, 128, 128)
MARIO_RED = (255, 0, 0)
MARIO_BLUE = (0, 0, 255)

@dataclass
class Vector3D:
    """3D向量类"""
    x: float
    y: float
    z: float
    
    def __add__(self, other):
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other):
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar):
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)

class Camera3D:
    """3D摄像机类"""
    def __init__(self):
        self.position = Vector3D(0, 5, 10)
        self.target = Vector3D(0, 0, 0)
        self.distance = 300  # 投影距离
        
    def project_point(self, point: Vector3D) -> Tuple[int, int]:
        """将3D点投影到2D屏幕坐标"""
        # 相对于摄像机的位置
        relative = point - self.position
        
        # 简单的透视投影
        if relative.z != 0:
            screen_x = int(SCREEN_WIDTH // 2 + (relative.x * self.distance) / (relative.z + self.distance))
            screen_y = int(SCREEN_HEIGHT // 2 - (relative.y * self.distance) / (relative.z + self.distance))
        else:
            screen_x = int(SCREEN_WIDTH // 2 + relative.x * 10)
            screen_y = int(SCREEN_HEIGHT // 2 - relative.y * 10)
            
        return (screen_x, screen_y)
    
    def follow_target(self, target_pos: Vector3D):
        """摄像机跟随目标"""
        self.position.x = target_pos.x - 5
        self.position.y = target_pos.y + 3
        self.position.z = target_pos.z + 8

class Mario3D:
    """3D玛丽角色类"""
    def __init__(self):
        self.position = Vector3D(0, 0, 0)
        self.velocity = Vector3D(0, 0, 0)
        self.size = 1.0
        self.on_ground = False
        self.speed = 0.2
        self.jump_power = 0.5
        self.gravity = -0.02
        
    def update(self, keys_pressed):
        """更新玛丽状态"""
        # 水平移动
        if keys_pressed[pygame.K_a] or keys_pressed[pygame.K_LEFT]:
            self.velocity.x = -self.speed
        elif keys_pressed[pygame.K_d] or keys_pressed[pygame.K_RIGHT]:
            self.velocity.x = self.speed
        else:
            self.velocity.x *= 0.8  # 摩擦力
            
        if keys_pressed[pygame.K_w] or keys_pressed[pygame.K_UP]:
            self.velocity.z = -self.speed
        elif keys_pressed[pygame.K_s] or keys_pressed[pygame.K_DOWN]:
            self.velocity.z = self.speed
        else:
            self.velocity.z *= 0.8  # 摩擦力
            
        # 跳跃
        if (keys_pressed[pygame.K_SPACE] or keys_pressed[pygame.K_j]) and self.on_ground:
            self.velocity.y = self.jump_power
            self.on_ground = False
            
        # 重力
        self.velocity.y += self.gravity
        
        # 更新位置
        self.position = self.position + self.velocity
        
        # 地面碰撞检测
        if self.position.y <= 0:
            self.position.y = 0
            self.velocity.y = 0
            self.on_ground = True
    
    def draw(self, screen, camera: Camera3D):
        """绘制3D玛丽"""
        # 玛丽的身体 - 立方体
        body_points = [
            Vector3D(self.position.x - 0.5, self.position.y + 0.5, self.position.z - 0.5),
            Vector3D(self.position.x + 0.5, self.position.y + 0.5, self.position.z - 0.5),
            Vector3D(self.position.x + 0.5, self.position.y + 1.5, self.position.z - 0.5),
            Vector3D(self.position.x - 0.5, self.position.y + 1.5, self.position.z - 0.5),
            Vector3D(self.position.x - 0.5, self.position.y + 0.5, self.position.z + 0.5),
            Vector3D(self.position.x + 0.5, self.position.y + 0.5, self.position.z + 0.5),
            Vector3D(self.position.x + 0.5, self.position.y + 1.5, self.position.z + 0.5),
            Vector3D(self.position.x - 0.5, self.position.y + 1.5, self.position.z + 0.5),
        ]
        
        # 投影到2D
        projected_points = [camera.project_point(p) for p in body_points]
        
        # 绘制立方体的面
        faces = [
            [0, 1, 2, 3],  # 前面
            [4, 5, 6, 7],  # 后面
            [0, 1, 5, 4],  # 底面
            [2, 3, 7, 6],  # 顶面
            [0, 3, 7, 4],  # 左面
            [1, 2, 6, 5],  # 右面
        ]
        
        colors = [MARIO_RED, MARIO_RED, BROWN, MARIO_RED, MARIO_BLUE, MARIO_BLUE]
        
        for i, face in enumerate(faces):
            points = [projected_points[j] for j in face]
            if len(points) >= 3:
                pygame.draw.polygon(screen, colors[i], points)
                pygame.draw.polygon(screen, BLACK, points, 2)

class Block3D:
    """3D方块类"""
    def __init__(self, position: Vector3D, color=BROWN, block_type="normal"):
        self.position = position
        self.color = color
        self.block_type = block_type
        self.size = 1.0
        
    def draw(self, screen, camera: Camera3D):
        """绘制3D方块"""
        # 方块的8个顶点
        points = [
            Vector3D(self.position.x - 0.5, self.position.y - 0.5, self.position.z - 0.5),
            Vector3D(self.position.x + 0.5, self.position.y - 0.5, self.position.z - 0.5),
            Vector3D(self.position.x + 0.5, self.position.y + 0.5, self.position.z - 0.5),
            Vector3D(self.position.x - 0.5, self.position.y + 0.5, self.position.z - 0.5),
            Vector3D(self.position.x - 0.5, self.position.y - 0.5, self.position.z + 0.5),
            Vector3D(self.position.x + 0.5, self.position.y - 0.5, self.position.z + 0.5),
            Vector3D(self.position.x + 0.5, self.position.y + 0.5, self.position.z + 0.5),
            Vector3D(self.position.x - 0.5, self.position.y + 0.5, self.position.z + 0.5),
        ]
        
        # 投影到2D
        projected_points = [camera.project_point(p) for p in points]
        
        # 绘制立方体的面
        faces = [
            [0, 1, 2, 3],  # 前面
            [4, 5, 6, 7],  # 后面
            [0, 1, 5, 4],  # 底面
            [2, 3, 7, 6],  # 顶面
            [0, 3, 7, 4],  # 左面
            [1, 2, 6, 5],  # 右面
        ]
        
        for face in faces:
            points_2d = [projected_points[j] for j in face]
            if len(points_2d) >= 3:
                pygame.draw.polygon(screen, self.color, points_2d)
                pygame.draw.polygon(screen, BLACK, points_2d, 1)

class Game3D:
    """3D游戏主类"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("3D超级玛丽")
        self.clock = pygame.time.Clock()
        self.camera = Camera3D()
        self.mario = Mario3D()
        self.blocks = self.create_level()
        
    def create_level(self) -> List[Block3D]:
        """创建关卡"""
        blocks = []
        
        # 地面
        for x in range(-10, 11):
            for z in range(-5, 6):
                blocks.append(Block3D(Vector3D(x, -1, z), GREEN))
        
        # 一些平台
        for x in range(3, 8):
            blocks.append(Block3D(Vector3D(x, 1, 0), BROWN))
            
        for x in range(-8, -3):
            blocks.append(Block3D(Vector3D(x, 2, 2), BROWN))
            
        # 管道
        for y in range(0, 4):
            blocks.append(Block3D(Vector3D(10, y, 0), GREEN))
            blocks.append(Block3D(Vector3D(10, y, 1), GREEN))
            
        return blocks
        
    def run(self):
        """游戏主循环"""
        running = True
        
        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
            
            # 获取按键状态
            keys_pressed = pygame.key.get_pressed()
            
            # 更新游戏状态
            self.mario.update(keys_pressed)
            self.camera.follow_target(self.mario.position)
            
            # 绘制
            self.screen.fill(WHITE)
            
            # 绘制所有方块
            for block in self.blocks:
                block.draw(self.screen, self.camera)
            
            # 绘制玛丽
            self.mario.draw(self.screen, self.camera)
            
            # 绘制UI信息
            font = pygame.font.Font(None, 36)
            info_text = font.render(f"位置: ({self.mario.position.x:.1f}, {self.mario.position.y:.1f}, {self.mario.position.z:.1f})", True, BLACK)
            self.screen.blit(info_text, (10, 10))
            
            control_text = font.render("控制: WASD移动, 空格跳跃, ESC退出", True, BLACK)
            self.screen.blit(control_text, (10, 50))
            
            pygame.display.flip()
            self.clock.tick(FPS)
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game3D()
    game.run()
