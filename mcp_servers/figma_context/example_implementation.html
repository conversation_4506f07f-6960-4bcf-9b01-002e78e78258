<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Figma设计实现示例</title>
    <style>
        /* 这些样式模拟从Figma导出的设计 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        .login-container {
            width: 400px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .login-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 24px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #E0E0E0;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: #4F46E5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
        }

        .login-button {
            width: 100%;
            padding: 12px;
            background-color: #4F46E5;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .login-button:hover {
            background-color: #4338CA;
        }

        .login-footer {
            margin-top: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }

        .login-footer a {
            color: #4F46E5;
            text-decoration: none;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }

        /* 添加一个简单的动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .login-container {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <!-- 这个HTML结构基于我们假设从Figma获取的设计数据 -->
    <div class="login-container">
        <h1 class="login-title">欢迎登录</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" class="form-control" placeholder="请输入您的用户名" required>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" class="form-control" placeholder="请输入您的密码" required>
            </div>
            <button type="submit" class="login-button">登录</button>
            <div class="login-footer">
                <p>还没有账号？<a href="#">立即注册</a></p>
                <p><a href="#">忘记密码？</a></p>
            </div>
        </form>
    </div>

    <script>
        // 简单的表单处理逻辑
        document.getElementById('loginForm').addEventListener('submit', function(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            console.log('登录信息:', { username, password });
            alert(`登录成功！欢迎回来，${username}！`);
            
            // 在实际应用中，这里会发送登录请求到服务器
        });

        // 在实际应用中，这里的代码会基于从Figma MCP服务器获取的设计数据动态生成
        console.log('这个页面是基于Figma设计实现的示例');
        console.log('在实际应用中，您会使用MCP服务器从Figma获取设计数据，然后生成类似的代码');
    </script>
</body>
</html>