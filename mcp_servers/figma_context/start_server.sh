#!/bin/bash

# Figma Context MCP服务器启动脚本
# 使用方法: ./start_server.sh

# 检查是否已安装Node.js和npm
if ! command -v node &> /dev/null || ! command -v npm &> /dev/null; then
    echo "错误: 未找到Node.js或npm。请先安装Node.js: https://nodejs.org/"
    exit 1
fi

# 检查配置文件是否存在
CONFIG_FILE="mcp_settings.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 未找到配置文件 $CONFIG_FILE"
    exit 1
fi

# 检查是否已配置Figma API访问令牌
if grep -q "YOUR-KEY" "$CONFIG_FILE"; then
    echo "错误: 请先在 $CONFIG_FILE 中配置您的Figma API访问令牌"
    echo "将 YOUR-KEY 替换为您的实际访问令牌"
    exit 1
fi

# 检查是否已安装figma-developer-mcp
if ! npm list -g figma-developer-mcp &> /dev/null; then
    echo "正在全局安装figma-developer-mcp..."
    npm install -g figma-developer-mcp
fi

echo "正在启动Figma Context MCP服务器..."
echo "服务器将在后台运行。要停止服务器，请按Ctrl+C"

# 从配置文件中提取API密钥
API_KEY=$(grep -o '"--figma-api-key=.*"' "$CONFIG_FILE" | sed 's/"--figma-api-key=\(.*\)"/\1/')

# 启动MCP服务器
# 注意：在实际使用中，您的IDE会负责启动MCP服务器
# 这个脚本仅用于测试目的
npx figma-developer-mcp --figma-api-key="$API_KEY" --port=3333

echo "MCP服务器已停止"