# Figma Context MCP 服务器安装指南

本指南将帮助您设置Figma Context MCP服务器，使您的AI编码工具（如Cursor）能够访问Figma设计数据。

## 前提条件

- Node.js 和 npm 已安装
- Figma账户和API访问令牌

## 获取Figma API访问令牌

1. 登录您的Figma账户
2. 访问 [Figma 个人访问令牌页面](https://help.figma.com/hc/en-us/articles/8085703771159-Manage-personal-access-tokens)
3. 创建一个新的访问令牌
4. 复制生成的令牌（请妥善保管，它只会显示一次）

## 安装步骤

1. **更新配置文件**

   打开 `mcp_settings.json` 文件，将 `YOUR-KEY` 替换为您的Figma API访问令牌：

   ```json
   {
     "mcpServers": {
       "github.com/GLips/Figma-Context-MCP": {
         "command": "npx",
         "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR-ACTUAL-KEY", "--stdio"]
       }
     }
   }
   ```

2. **安装依赖**

   在终端中运行以下命令安装 figma-developer-mcp 包：

   ```bash
   npm install -g figma-developer-mcp
   ```

3. **设置启动脚本权限**

   为启动脚本设置执行权限：

   ```bash
   chmod +x start_server.sh
   ```

4. **启动MCP服务器**

   您可以通过以下两种方式之一启动MCP服务器：

   **方式一：使用提供的启动脚本**
   
   ```bash
   ./start_server.sh
   ```
   
   这将在端口3333上启动MCP服务器。

   **方式二：在IDE中配置**
   
   在您的IDE（如Cursor）中配置MCP服务器。具体步骤取决于您使用的IDE，但通常包括：
   
   - 在IDE的设置中找到MCP服务器配置部分
   - 添加新的MCP服务器，指向您的 `mcp_settings.json` 文件
   - 重启IDE以应用更改

## 使用方法

一旦MCP服务器设置完成，您可以通过以下方式使用它：

1. 打开您的IDE的聊天功能（例如Cursor中的agent模式）
2. 粘贴Figma文件、框架或组的链接
3. 要求您的AI助手使用该Figma文件执行某些操作（例如实现设计）
4. AI将从Figma获取相关元数据并使用它来编写代码

## 示例提示

```
请使用这个Figma设计实现一个登录页面：https://www.figma.com/file/your-file-id/your-file-name?node-id=your-node-id
```

## 故障排除

如果遇到问题：

- 确保您的Figma API访问令牌有效且具有适当的权限
- 检查您的网络连接
- 确保您的IDE正确配置了MCP服务器
- 查看IDE的日志以获取更详细的错误信息

## 更多资源

- [Framelink 文档](https://www.framelink.ai/docs/quickstart)
- [Figma API 文档](https://www.figma.com/developers/api)
- [Model Context Protocol 文档](https://modelcontextprotocol.io/introduction)

## 示例文件

本目录包含以下示例文件，帮助您了解如何使用Figma Context MCP服务器：

1. **example_usage.js** - 展示如何在代码中与MCP服务器交互并使用Figma设计数据生成React组件
2. **example_implementation.html** - 一个基于Figma设计实现的HTML登录页面示例
3. **start_server.sh** - 用于启动MCP服务器的脚本

这些示例文件仅用于演示目的，不需要实际运行。在实际使用中，您的IDE会负责与MCP服务器交互。