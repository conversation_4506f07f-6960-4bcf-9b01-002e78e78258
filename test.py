# import openai

# def chat_with_ollama(prompt, model="deepseek-r1:32b", base_url="http://47.99.120.27:11434/v1"):
#     """
#     使用OpenAI库与Ollama模型聊天
    
#     参数:
#         prompt (str): 用户输入的提示词
#         model (str): 要使用的模型名称，默认为deepseek-r1:7b
#         base_url (str): Ollama API的基础URL
        
#     返回:
#         str: 模型的回复
#     """
#     # 配置OpenAI客户端
#     client = openai.OpenAI(
#         base_url=base_url,
#         api_key="ollama"  # Ollama不需要真实的API key，但需要提供非空值
#     )
    
#     try:
#         # 创建聊天完成请求
#         response = client.chat.completions.create(
#             model=model,
#             messages=[
#                 {"role": "system", "content": "你是一个乐于助人的AI助手。"},
#                 {"role": "user", "content": prompt}
#             ]
#         )
        
#         # 返回模型的回复
#         return response.choices[0].message.content
        
#     except Exception as e:
#         return f"发生错误: {str(e)}"

# # 使用示例
# if __name__ == "__main__":
#     while True:
#         user_input = input("\n你: ")
#         if user_input.lower() in ["退出", "exit", "quit"]:
#             print("再见！")
#             break
            
#         response = chat_with_ollama(user_input)
#         print(f"\nAI: {response}")



import openai

def chat_with_ollama_stream(prompt, model="deepseek-r1:70B", base_url="http://39.174.37.169:11434/v1"):
    """
    使用OpenAI库与Ollama模型进行流式聊天
    
    参数:
        prompt (str): 用户输入的提示词
        model (str): 要使用的模型名称，默认为deepseek-r1:7b
        base_url (str): Ollama API的基础URL
    """
    # 配置OpenAI客户端
    client = openai.OpenAI(
        base_url=base_url,
        api_key="ollama"  # Ollama不需要真实的API key，但需要提供非空值
    )
    
    try:
        # 创建流式聊天完成请求
        stream = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "你是一个乐于助人的AI助手。"},
                {"role": "user", "content": prompt}
            ],
            stream=True  # 启用流式输出
        )
        
        print("\nAI: ", end="", flush=True)
        full_response = ""
        
        # 逐块输出响应
        for chunk in stream:
            content = chunk.choices[0].delta.content or ""
            print(content, end="", flush=True)
            full_response += content
            
        return full_response
        
    except Exception as e:
        error_msg = f"发生错误: {str(e)}"
        print(error_msg)
        return error_msg

# 使用示例
if __name__ == "__main__":
    while True:
        user_input = input("\n你: ")
        if user_input.lower() in ["退出", "exit", "quit"]:
            print("再见！")
            break
            
        chat_with_ollama_stream(user_input)