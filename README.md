# 3D超级玛丽游戏

一个使用Python和Ursina引擎开发的3D版超级玛丽游戏。

## 功能特性

### 核心游戏机制
- **3D角色控制**: 玛丽可以在3D空间中移动、跳跃和转向
- **物理引擎**: 真实的重力、碰撞检测和物理反馈
- **经典元素**: 管道、砖块、问号方块、金币等经典超级玛丽元素
- **敌人系统**: 栗子怪AI和互动机制
- **道具系统**: 蘑菇变大、花朵发射等经典道具

### 3D视觉效果
- **立体场景**: 完全3D化的游戏世界
- **动态摄像机**: 跟随玛丽的3D摄像机系统
- **3D模型**: 所有游戏对象都是3D模型
- **光影效果**: 基本的3D光照和阴影

### 控制方式
- **WASD**: 移动控制
- **空格**: 跳跃
- **鼠标**: 摄像机视角控制

## 技术栈

- **Python 3.13+**: 主要编程语言
- **Ursina引擎**: 3D游戏引擎
- **Panda3D**: 底层3D渲染引擎

## 安装和运行

### 安装依赖
```bash
uv add pygame
```

### 运行游戏
```bash
# 推荐：运行演示程序
uv run python demo.py

# 或直接运行游戏
uv run python mario_3d.py
```

## 项目结构

```
├── mario_3d.py          # 主游戏文件（完整版）
├── mario_3d_simple.py   # 简化版游戏
├── player.py            # 玛丽角色控制系统（Ursina版本）
├── enemy.py             # 敌人AI系统
├── game_objects.py      # 游戏道具和障碍物（Ursina版本）
├── demo.py              # 游戏演示脚本
└── README.md            # 项目文档
```

## 开发计划

### 第一阶段 - 基础框架 ✅
- [x] 项目初始化和依赖安装
- [x] 基本3D场景搭建
- [x] 玛丽角色基础控制

### 第二阶段 - 核心机制 ✅
- [x] 物理引擎集成
- [x] 碰撞检测系统
- [x] 基础关卡设计

### 第三阶段 - 游戏内容 ✅
- [x] 敌人系统（栗子怪、乌龟）
- [x] 道具收集（金币）
- [x] 生命值和分数系统

### 第四阶段 - 优化完善 ✅
- [x] 游戏界面和UI
- [x] 演示系统
- [x] 项目文档完善

## 游戏版本

### 完整版 (mario_3d.py)
包含所有游戏功能的完整版本：
- 完整的3D渲染系统
- 智能敌人AI（栗子怪和乌龟）
- 道具收集系统
- 碰撞检测和物理引擎
- 生命值和分数系统
- 完整的用户界面

### 简化版 (mario_3d_simple.py)
展示基础3D技术的简化版本：
- 基础3D投影和渲染
- 简单的角色控制
- 基础场景元素
- 适合学习3D游戏开发原理

## 快速开始

### 运行演示
```bash
uv run python demo.py
```

### 直接运行游戏
```bash
# 完整版
uv run python mario_3d.py

# 简化版
uv run python mario_3d_simple.py
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License