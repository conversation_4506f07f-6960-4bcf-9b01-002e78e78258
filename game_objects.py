"""
3D超级玛丽 - 游戏对象系统
包含方块、道具、敌人等游戏元素
"""

from ursina import *
import random
import math

class Block(Entity):
    """基础方块类"""
    
    def __init__(self, position=(0,0,0), block_type="brick", **kwargs):
        super().__init__()
        
        self.block_type = block_type
        self.model = 'cube'
        self.position = position
        self.scale = (1, 1, 1)
        
        # 根据方块类型设置外观
        if block_type == "brick":
            self.color = color.brown
            self.texture = 'brick'
        elif block_type == "question":
            self.color = color.yellow
            self.has_item = True
            self.item_type = random.choice(["coin", "mushroom", "fire_flower"])
        elif block_type == "pipe":
            self.color = color.green
            self.scale = (1, 2, 1)
        elif block_type == "ground":
            self.color = color.dark_green
        elif block_type == "platform":
            self.color = color.gray
        else:
            self.color = color.white
            
        # 碰撞检测
        self.collider = 'box'
        
    def hit_from_below(self, player):
        """从下方被撞击"""
        if self.block_type == "question" and hasattr(self, 'has_item') and self.has_item:
            self.spawn_item(player)
            self.has_item = False
            self.color = color.brown  # 变成普通方块
        elif self.block_type == "brick":
            if player.power_level > 1:
                # 大玛丽可以破坏砖块
                self.break_block()
            else:
                # 小玛丽只能顶一下
                self.bounce()
                
    def spawn_item(self, player):
        """生成道具"""
        if hasattr(self, 'item_type'):
            if self.item_type == "coin":
                Coin(position=self.position + Vec3(0, 1, 0))
            elif self.item_type == "mushroom":
                Mushroom(position=self.position + Vec3(0, 1, 0))
            elif self.item_type == "fire_flower":
                FireFlower(position=self.position + Vec3(0, 1, 0))
                
    def break_block(self):
        """破坏方块"""
        # 创建破碎效果
        for i in range(4):
            fragment = Entity(
                model='cube',
                color=self.color,
                scale=0.3,
                position=self.position + Vec3(random.uniform(-0.5, 0.5), 0, random.uniform(-0.5, 0.5))
            )
            fragment.velocity = Vec3(random.uniform(-5, 5), random.uniform(5, 10), random.uniform(-5, 5))
            
            def update_fragment():
                fragment.position += fragment.velocity * time.dt
                fragment.velocity.y -= 20 * time.dt
                fragment.rotation += Vec3(random.uniform(-180, 180), random.uniform(-180, 180), 0) * time.dt
                
                if fragment.position.y < -10:
                    destroy(fragment)
                    
            fragment.update = update_fragment
            
        destroy(self)
        
    def bounce(self):
        """弹跳效果"""
        # 简单的弹跳动画
        original_y = self.y
        self.animate_y(original_y - 0.2, duration=0.1)
        invoke(lambda: self.animate_y(original_y, duration=0.1), delay=0.1)

class Coin(Entity):
    """金币类"""
    
    def __init__(self, position=(0,0,0), **kwargs):
        super().__init__()
        
        self.model = 'cylinder'
        self.color = color.yellow
        self.scale = (0.5, 0.1, 0.5)
        self.position = position
        self.collider = 'box'
        
        # 旋转动画
        self.animate_rotation_y(360, duration=2, curve=curve.linear, loop=True)
        
        # 上下浮动
        self.animate_y(self.y + 0.3, duration=1, curve=curve.in_out_sine, loop=True)
        
    def collect(self, player):
        """被收集"""
        player.collect_coin()
        
        # 收集效果
        self.animate_scale(0, duration=0.2)
        self.animate_y(self.y + 2, duration=0.2)
        
        destroy(self, delay=0.2)

class Mushroom(Entity):
    """蘑菇道具类"""
    
    def __init__(self, position=(0,0,0), **kwargs):
        super().__init__()
        
        self.model = 'cube'
        self.color = color.red
        self.scale = (0.8, 0.8, 0.8)
        self.position = position
        self.collider = 'box'
        
        # 移动属性
        self.velocity = Vec3(2, 0, 0)
        self.gravity = -15
        
    def update(self):
        """更新蘑菇移动"""
        # 应用重力
        self.velocity.y += self.gravity * time.dt
        
        # 更新位置
        self.position += self.velocity * time.dt
        
        # 简单的地面检测
        if self.position.y <= 0.5:
            self.position = (self.position.x, 0.5, self.position.z)
            self.velocity.y = 0
            
        # 如果掉出世界就销毁
        if self.position.y < -10:
            destroy(self)
            
    def collect(self, player):
        """被收集"""
        player.collect_powerup("mushroom")
        destroy(self)

class FireFlower(Entity):
    """火焰花道具类"""
    
    def __init__(self, position=(0,0,0), **kwargs):
        super().__init__()
        
        self.model = 'cube'
        self.color = color.orange
        self.scale = (0.6, 0.6, 0.6)
        self.position = position
        self.collider = 'box'
        
        # 闪烁效果
        self.animate_color(color.red, duration=0.5, curve=curve.in_out_sine, loop=True)
        
    def collect(self, player):
        """被收集"""
        player.collect_powerup("fire_flower")
        destroy(self)

class Goomba(Entity):
    """栗子怪敌人类"""
    
    def __init__(self, position=(0,0,0), **kwargs):
        super().__init__()
        
        self.model = 'cube'
        self.color = color.brown
        self.scale = (0.8, 0.8, 0.8)
        self.position = position
        self.collider = 'box'
        
        # AI属性
        self.velocity = Vec3(-1, 0, 0)  # 向左移动
        self.gravity = -15
        self.health = 1
        self.is_alive = True
        
        # 巡逻范围
        self.start_x = position[0]
        self.patrol_range = 5
        
    def update(self):
        """更新敌人AI"""
        if not self.is_alive:
            return
            
        # 应用重力
        self.velocity.y += self.gravity * time.dt
        
        # 更新位置
        self.position += self.velocity * time.dt
        
        # 地面检测
        if self.position.y <= 0.5:
            self.position = (self.position.x, 0.5, self.position.z)
            self.velocity.y = 0
            
        # 巡逻逻辑
        if abs(self.position.x - self.start_x) > self.patrol_range:
            self.velocity.x *= -1  # 转向
            
        # 简单的移动动画
        self.rotation_y += 50 * time.dt
        
    def take_damage(self, player):
        """受到伤害"""
        if not self.is_alive:
            return
            
        self.health -= 1
        if self.health <= 0:
            self.die(player)
            
    def die(self, player):
        """死亡"""
        self.is_alive = False
        player.add_score(100)
        
        # 死亡动画
        self.animate_scale(0, duration=0.3)
        self.animate_rotation_z(180, duration=0.3)
        
        destroy(self, delay=0.3)
        
    def check_collision_with_player(self, player):
        """检查与玩家的碰撞"""
        if not self.is_alive:
            return
            
        distance = distance(self.position, player.position)
        if distance < 1.5:
            # 检查玩家是否从上方踩踏
            if player.position.y > self.position.y + 0.5 and player.velocity.y < 0:
                # 被踩踏
                self.take_damage(player)
                player.velocity.y = 10  # 给玩家一个小跳跃
            else:
                # 玩家受伤
                player.take_damage()

class Pipe(Entity):
    """管道类"""
    
    def __init__(self, position=(0,0,0), height=2, **kwargs):
        super().__init__()
        
        self.model = 'cube'
        self.color = color.green
        self.scale = (1, height, 1)
        self.position = position
        self.collider = 'box'
        
        # 管道顶部
        self.top = Entity(
            model='cube',
            color=color.dark_green,
            scale=(1.2, 0.2, 1.2),
            position=position + Vec3(0, height/2 + 0.1, 0),
            collider='box'
        )
        
    def enter_pipe(self, player):
        """进入管道"""
        # 这里可以实现传送或进入地下关卡的逻辑
        print("进入管道!")

class Platform(Entity):
    """移动平台类"""
    
    def __init__(self, position=(0,0,0), move_distance=5, move_speed=2, **kwargs):
        super().__init__()
        
        self.model = 'cube'
        self.color = color.gray
        self.scale = (3, 0.2, 1)
        self.position = position
        self.collider = 'box'
        
        # 移动属性
        self.start_position = Vec3(position)
        self.move_distance = move_distance
        self.move_speed = move_speed
        self.direction = 1
        
    def update(self):
        """更新平台移动"""
        # 水平移动
        self.position += Vec3(self.direction * self.move_speed * time.dt, 0, 0)
        
        # 检查移动范围
        if abs(self.position.x - self.start_position.x) > self.move_distance:
            self.direction *= -1  # 改变方向

class Star(Entity):
    """星星道具类"""
    
    def __init__(self, position=(0,0,0), **kwargs):
        super().__init__()
        
        self.model = 'cube'  # 可以替换为星星模型
        self.color = color.yellow
        self.scale = (0.6, 0.6, 0.6)
        self.position = position
        self.collider = 'box'
        
        # 闪烁和旋转效果
        self.animate_rotation_y(360, duration=1, curve=curve.linear, loop=True)
        self.animate_scale(0.8, duration=0.5, curve=curve.in_out_sine, loop=True)
        
    def collect(self, player):
        """被收集"""
        player.collect_powerup("star")
        destroy(self)
