#!/usr/bin/env python3
"""
3D超级玛丽游戏 - 主游戏文件
使用Pygame实现的3D超级玛丽游戏
"""

import pygame
import math
import sys
import random
from typing import List, Tuple, Dict
from dataclasses import dataclass
from enemy import EnemyManager, Vector3D as EnemyVector3D

# 初始化Pygame
pygame.init()

# 游戏常量
SCREEN_WIDTH = 1400
SCREEN_HEIGHT = 900
FPS = 60

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
BROWN = (139, 69, 19)
GRAY = (128, 128, 128)
DARK_GREEN = (0, 128, 0)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)
MARIO_RED = (255, 0, 0)
MARIO_BLUE = (0, 0, 255)
SKY_BLUE = (135, 206, 235)

@dataclass
class Vector3D:
    """3D向量类"""
    x: float
    y: float
    z: float
    
    def __add__(self, other):
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other):
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar):
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def distance_to(self, other):
        """计算到另一个向量的距离"""
        dx = self.x - other.x
        dy = self.y - other.y
        dz = self.z - other.z
        return math.sqrt(dx*dx + dy*dy + dz*dz)

class Camera3D:
    """3D摄像机类"""
    def __init__(self):
        self.position = Vector3D(0, 5, 10)
        self.target = Vector3D(0, 0, 0)
        self.distance = 400  # 投影距离
        self.angle_x = 0  # 上下角度
        self.angle_y = 0  # 左右角度
        
    def project_point(self, point: Vector3D) -> Tuple[int, int]:
        """将3D点投影到2D屏幕坐标"""
        # 相对于摄像机的位置
        relative = point - self.position
        
        # 应用摄像机旋转
        cos_y = math.cos(self.angle_y)
        sin_y = math.sin(self.angle_y)
        cos_x = math.cos(self.angle_x)
        sin_x = math.sin(self.angle_x)
        
        # Y轴旋转
        x = relative.x * cos_y - relative.z * sin_y
        z = relative.x * sin_y + relative.z * cos_y
        y = relative.y
        
        # X轴旋转
        y_rot = y * cos_x - z * sin_x
        z_rot = y * sin_x + z * cos_x
        
        # 透视投影
        if z_rot + self.distance > 0:
            screen_x = int(SCREEN_WIDTH // 2 + (x * self.distance) / (z_rot + self.distance))
            screen_y = int(SCREEN_HEIGHT // 2 - (y_rot * self.distance) / (z_rot + self.distance))
        else:
            screen_x = int(SCREEN_WIDTH // 2 + x * 10)
            screen_y = int(SCREEN_HEIGHT // 2 - y_rot * 10)
            
        return (screen_x, screen_y)
    
    def follow_target(self, target_pos: Vector3D):
        """摄像机跟随目标"""
        self.position.x = target_pos.x - 8
        self.position.y = target_pos.y + 5
        self.position.z = target_pos.z + 12

class Mario3D:
    """3D玛丽角色类"""
    def __init__(self):
        self.position = Vector3D(0, 1, 0)
        self.velocity = Vector3D(0, 0, 0)
        self.size = 1.0
        self.on_ground = False
        self.speed = 0.3
        self.jump_power = 0.8
        self.gravity = -0.03
        self.health = 3
        self.score = 0
        self.power_level = 1  # 1=小玛丽, 2=大玛丽, 3=火焰玛丽
        self.invincible = False
        self.invincible_timer = 0
        self.facing_direction = 1  # 1=右, -1=左

    def take_damage(self):
        """受到伤害"""
        if self.invincible:
            return

        if self.power_level > 1:
            # 降级
            self.power_level -= 1
            self.invincible = True
            self.invincible_timer = 2.0
        else:
            # 失去生命
            self.health -= 1
            if self.health <= 0:
                self.game_over()
            else:
                # 重置位置
                self.position = Vector3D(0, 1, 0)
                self.velocity = Vector3D(0, 0, 0)

    def game_over(self):
        """游戏结束"""
        print(f"游戏结束! 最终得分: {self.score}")
        # 重置游戏状态
        self.__init__()
        
    def update(self, keys_pressed, blocks):
        """更新玛丽状态"""
        # 水平移动
        if keys_pressed[pygame.K_a] or keys_pressed[pygame.K_LEFT]:
            self.velocity.x = -self.speed
            self.facing_direction = -1
        elif keys_pressed[pygame.K_d] or keys_pressed[pygame.K_RIGHT]:
            self.velocity.x = self.speed
            self.facing_direction = 1
        else:
            self.velocity.x *= 0.8  # 摩擦力
            
        if keys_pressed[pygame.K_w] or keys_pressed[pygame.K_UP]:
            self.velocity.z = -self.speed
        elif keys_pressed[pygame.K_s] or keys_pressed[pygame.K_DOWN]:
            self.velocity.z = self.speed
        else:
            self.velocity.z *= 0.8  # 摩擦力
            
        # 跳跃
        if (keys_pressed[pygame.K_SPACE] or keys_pressed[pygame.K_j]) and self.on_ground:
            self.velocity.y = self.jump_power
            self.on_ground = False
            
        # 重力
        self.velocity.y += self.gravity
        
        # 更新位置
        new_position = self.position + self.velocity
        
        # 碰撞检测
        self.check_collisions(new_position, blocks)
        
        # 地面碰撞检测
        if self.position.y <= 0.5:
            self.position.y = 0.5
            self.velocity.y = 0
            self.on_ground = True
        else:
            self.on_ground = False
            
        # 更新无敌状态
        if self.invincible:
            self.invincible_timer -= 1/60  # 假设60FPS
            if self.invincible_timer <= 0:
                self.invincible = False
    
    def check_collisions(self, new_position, blocks):
        """检查与方块的碰撞"""
        # 简单的碰撞检测
        for block in blocks:
            if (abs(new_position.x - block.position.x) < 1.0 and
                abs(new_position.y - block.position.y) < 1.0 and
                abs(new_position.z - block.position.z) < 1.0):
                
                # 如果是从上方碰撞，停在方块上
                if self.velocity.y < 0 and self.position.y > block.position.y:
                    self.position.y = block.position.y + 1.0
                    self.velocity.y = 0
                    self.on_ground = True
                    return
                    
                # 水平碰撞，阻止移动
                if abs(self.position.x - block.position.x) < 1.0:
                    new_position.x = self.position.x
                if abs(self.position.z - block.position.z) < 1.0:
                    new_position.z = self.position.z
        
        self.position = new_position
    
    def draw(self, screen, camera: Camera3D):
        """绘制3D玛丽"""
        # 根据能力等级确定颜色和大小
        if self.power_level == 1:
            color = MARIO_RED
            height = 1.0
        elif self.power_level == 2:
            color = ORANGE
            height = 1.5
        else:
            color = YELLOW
            height = 1.5
            
        # 无敌状态闪烁
        if self.invincible and int(self.invincible_timer * 10) % 2:
            color = WHITE
        
        # 玛丽的身体 - 立方体
        body_points = [
            Vector3D(self.position.x - 0.4, self.position.y, self.position.z - 0.4),
            Vector3D(self.position.x + 0.4, self.position.y, self.position.z - 0.4),
            Vector3D(self.position.x + 0.4, self.position.y + height, self.position.z - 0.4),
            Vector3D(self.position.x - 0.4, self.position.y + height, self.position.z - 0.4),
            Vector3D(self.position.x - 0.4, self.position.y, self.position.z + 0.4),
            Vector3D(self.position.x + 0.4, self.position.y, self.position.z + 0.4),
            Vector3D(self.position.x + 0.4, self.position.y + height, self.position.z + 0.4),
            Vector3D(self.position.x - 0.4, self.position.y + height, self.position.z + 0.4),
        ]
        
        # 投影到2D
        projected_points = [camera.project_point(p) for p in body_points]
        
        # 绘制立方体的面
        faces = [
            ([0, 1, 2, 3], color),  # 前面
            ([4, 5, 6, 7], color),  # 后面
            ([0, 1, 5, 4], BROWN),  # 底面
            ([2, 3, 7, 6], color),  # 顶面
            ([0, 3, 7, 4], MARIO_BLUE),  # 左面
            ([1, 2, 6, 5], MARIO_BLUE),  # 右面
        ]
        
        for face_indices, face_color in faces:
            points = [projected_points[i] for i in face_indices]
            if len(points) >= 3:
                pygame.draw.polygon(screen, face_color, points)
                pygame.draw.polygon(screen, BLACK, points, 2)

class Block3D:
    """3D方块类"""
    def __init__(self, position: Vector3D, color=BROWN, block_type="normal"):
        self.position = position
        self.color = color
        self.block_type = block_type
        self.size = 1.0
        
    def draw(self, screen, camera: Camera3D):
        """绘制3D方块"""
        # 方块的8个顶点
        points = [
            Vector3D(self.position.x - 0.5, self.position.y - 0.5, self.position.z - 0.5),
            Vector3D(self.position.x + 0.5, self.position.y - 0.5, self.position.z - 0.5),
            Vector3D(self.position.x + 0.5, self.position.y + 0.5, self.position.z - 0.5),
            Vector3D(self.position.x - 0.5, self.position.y + 0.5, self.position.z - 0.5),
            Vector3D(self.position.x - 0.5, self.position.y - 0.5, self.position.z + 0.5),
            Vector3D(self.position.x + 0.5, self.position.y - 0.5, self.position.z + 0.5),
            Vector3D(self.position.x + 0.5, self.position.y + 0.5, self.position.z + 0.5),
            Vector3D(self.position.x - 0.5, self.position.y + 0.5, self.position.z + 0.5),
        ]
        
        # 投影到2D
        projected_points = [camera.project_point(p) for p in points]
        
        # 绘制立方体的面
        faces = [
            [0, 1, 2, 3],  # 前面
            [4, 5, 6, 7],  # 后面
            [0, 1, 5, 4],  # 底面
            [2, 3, 7, 6],  # 顶面
            [0, 3, 7, 4],  # 左面
            [1, 2, 6, 5],  # 右面
        ]
        
        for face in faces:
            points_2d = [projected_points[i] for i in face]
            if len(points_2d) >= 3:
                pygame.draw.polygon(screen, self.color, points_2d)
                pygame.draw.polygon(screen, BLACK, points_2d, 1)

class Coin3D:
    """3D金币类"""
    def __init__(self, position: Vector3D):
        self.position = position
        self.rotation = 0
        self.collected = False
        
    def update(self):
        """更新金币状态"""
        self.rotation += 5  # 旋转动画
        
    def draw(self, screen, camera: Camera3D):
        """绘制3D金币"""
        if self.collected:
            return
            
        # 简单的圆形金币
        center = camera.project_point(self.position)
        pygame.draw.circle(screen, YELLOW, center, 15)
        pygame.draw.circle(screen, ORANGE, center, 15, 3)
        
    def check_collection(self, mario: Mario3D):
        """检查是否被收集"""
        if not self.collected and self.position.distance_to(mario.position) < 1.0:
            self.collected = True
            mario.score += 100
            return True
        return False

class Game3D:
    """3D游戏主类"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("3D超级玛丽 - 使用WASD移动，空格跳跃")
        self.clock = pygame.time.Clock()
        self.camera = Camera3D()
        self.mario = Mario3D()
        self.blocks = self.create_level()
        self.coins = self.create_coins()
        self.enemy_manager = EnemyManager()
        self.create_enemies()
        self.font = pygame.font.Font(None, 36)
        self.big_font = pygame.font.Font(None, 72)
        
    def create_level(self) -> List[Block3D]:
        """创建关卡"""
        blocks = []
        
        # 地面
        for x in range(-15, 16):
            for z in range(-8, 9):
                blocks.append(Block3D(Vector3D(x, -1, z), DARK_GREEN))
        
        # 平台和障碍
        for x in range(5, 10):
            blocks.append(Block3D(Vector3D(x, 1, 0), BROWN))
            
        for x in range(-10, -5):
            blocks.append(Block3D(Vector3D(x, 2, 3), BROWN))
            
        for x in range(12, 15):
            for y in range(0, 3):
                blocks.append(Block3D(Vector3D(x, y, 0), BROWN))
                
        # 管道
        for y in range(0, 4):
            blocks.append(Block3D(Vector3D(15, y, 0), GREEN))
            blocks.append(Block3D(Vector3D(15, y, 1), GREEN))
            
        # 问号方块
        blocks.append(Block3D(Vector3D(7, 3, 0), YELLOW))
        blocks.append(Block3D(Vector3D(-7, 4, 3), YELLOW))
            
        return blocks
        
    def create_coins(self) -> List[Coin3D]:
        """创建金币"""
        coins = []
        
        # 在平台上放置金币
        for x in range(6, 9):
            coins.append(Coin3D(Vector3D(x, 2.5, 0)))
            
        for x in range(-9, -6):
            coins.append(Coin3D(Vector3D(x, 3.5, 3)))
            
        # 随机放置一些金币
        for _ in range(10):
            x = random.randint(-12, 12)
            z = random.randint(-5, 5)
            coins.append(Coin3D(Vector3D(x, 1, z)))

        return coins

    def create_enemies(self):
        """创建敌人"""
        # 添加一些栗子怪
        self.enemy_manager.add_goomba(EnemyVector3D(8, 1, 0))
        self.enemy_manager.add_goomba(EnemyVector3D(-8, 3, 3))
        self.enemy_manager.add_goomba(EnemyVector3D(3, 1, -2))

        # 添加一些乌龟
        self.enemy_manager.add_koopa(EnemyVector3D(10, 1, 2))
        self.enemy_manager.add_koopa(EnemyVector3D(-5, 1, -3))
        
    def run(self):
        """游戏主循环"""
        running = True
        
        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_r:
                        # 重置游戏
                        self.mario = Mario3D()
                        self.coins = self.create_coins()
                        self.enemy_manager = EnemyManager()
                        self.create_enemies()
            
            # 获取按键状态
            keys_pressed = pygame.key.get_pressed()
            
            # 更新游戏状态
            self.mario.update(keys_pressed, self.blocks)
            self.camera.follow_target(self.mario.position)
            
            # 更新金币
            for coin in self.coins:
                coin.update()
                coin.check_collection(self.mario)

            # 更新敌人
            self.enemy_manager.update_all(self.mario, self.blocks)
            
            # 绘制
            self.screen.fill(SKY_BLUE)
            
            # 绘制所有方块
            for block in self.blocks:
                block.draw(self.screen, self.camera)
            
            # 绘制金币
            for coin in self.coins:
                coin.draw(self.screen, self.camera)

            # 绘制敌人
            self.enemy_manager.draw_all(self.screen, self.camera)

            # 绘制玛丽
            self.mario.draw(self.screen, self.camera)
            
            # 绘制UI信息
            self.draw_ui()
            
            pygame.display.flip()
            self.clock.tick(FPS)
        
        pygame.quit()
        sys.exit()
        
    def draw_ui(self):
        """绘制用户界面"""
        # 分数
        score_text = self.font.render(f"分数: {self.mario.score}", True, BLACK)
        self.screen.blit(score_text, (10, 10))
        
        # 生命值
        health_text = self.font.render(f"生命: {self.mario.health}", True, BLACK)
        self.screen.blit(health_text, (10, 50))
        
        # 位置信息
        pos_text = self.font.render(f"位置: ({self.mario.position.x:.1f}, {self.mario.position.y:.1f}, {self.mario.position.z:.1f})", True, BLACK)
        self.screen.blit(pos_text, (10, 90))
        
        # 能力等级
        power_text = self.font.render(f"能力等级: {self.mario.power_level}", True, BLACK)
        self.screen.blit(power_text, (10, 130))
        
        # 控制说明
        control_text = self.font.render("控制: WASD移动, 空格跳跃, R重置, ESC退出", True, BLACK)
        self.screen.blit(control_text, (10, SCREEN_HEIGHT - 40))
        
        # 收集的金币数量
        collected_coins = sum(1 for coin in self.coins if coin.collected)
        total_coins = len(self.coins)
        coin_text = self.font.render(f"金币: {collected_coins}/{total_coins}", True, BLACK)
        self.screen.blit(coin_text, (10, 170))

        # 敌人数量
        enemy_count = self.enemy_manager.get_enemy_count()
        enemy_text = self.font.render(f"敌人: {enemy_count}", True, BLACK)
        self.screen.blit(enemy_text, (10, 210))

if __name__ == "__main__":
    game = Game3D()
    game.run()
